"""
Google搜索服务
"""
import requests
import time
from typing import List, Dict, Any, Optional
from src.config import Config
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

class GoogleSearchService:
    """Google搜索服务类"""
    
    def __init__(self):
        """初始化Google搜索服务"""
        self.api_url = Config.GOOGLE_SEARCH_API_URL
        self.timeout = Config.SCRAPING_TIMEOUT
        self.max_retries = Config.MAX_RETRIES
        self.request_delay = Config.REQUEST_DELAY
        
        if not self.api_url:
            raise ValueError("Google搜索API URL未配置")
        
        logger.info("Google搜索服务初始化完成")
    
    def search(self, query: str, num_results: int = 10) -> List[Dict[str, Any]]:
        """
        执行Google搜索
        
        Args:
            query: 搜索查询词
            num_results: 返回结果数量
            
        Returns:
            搜索结果列表，每个结果包含title、url、snippet等字段
        """
        logger.info(f"执行Google搜索: {query}")
        
        for attempt in range(self.max_retries):
            try:
                # 构建请求参数
                params = {
                    'q': query,
                    'num': num_results
                }
                
                # 发送请求
                response = requests.get(
                    self.api_url,
                    params=params,
                    timeout=self.timeout
                )
                
                # 检查响应状态
                if response.status_code == 200:
                    data = response.json()
                    results = self._parse_search_results(data)
                    logger.info(f"搜索成功，返回 {len(results)} 个结果")
                    return results
                else:
                    logger.warning(f"搜索请求失败，状态码: {response.status_code}")
                    if attempt < self.max_retries - 1:
                        time.sleep(self.request_delay * (attempt + 1))
                        continue
                    else:
                        return []
                        
            except requests.exceptions.Timeout:
                logger.warning(f"搜索请求超时，尝试 {attempt + 1}/{self.max_retries}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1))
                    continue
                else:
                    logger.error("搜索请求最终超时")
                    return []
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"搜索请求异常: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1))
                    continue
                else:
                    return []
                    
            except Exception as e:
                logger.error(f"搜索过程中发生未知错误: {e}")
                return []
        
        return []
    
    def _parse_search_results(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        解析搜索结果数据
        
        Args:
            data: API返回的原始数据
            
        Returns:
            标准化的搜索结果列表
        """
        results = []
        
        try:
            # 根据API返回格式解析数据
            # 这里需要根据实际的Google搜索API响应格式进行调整
            if 'items' in data:
                for item in data['items']:
                    result = {
                        'title': item.get('title', ''),
                        'url': item.get('link', ''),
                        'snippet': item.get('snippet', ''),
                        'display_url': item.get('displayLink', '')
                    }
                    results.append(result)
            elif 'results' in data:
                for item in data['results']:
                    result = {
                        'title': item.get('title', ''),
                        'url': item.get('url', ''),
                        'snippet': item.get('description', ''),
                        'display_url': item.get('domain', '')
                    }
                    results.append(result)
            else:
                logger.warning("未知的搜索结果格式")
                
        except Exception as e:
            logger.error(f"解析搜索结果失败: {e}")
        
        return results
    
    def search_company_official_site(self, company_name: str) -> List[Dict[str, Any]]:
        """
        专门搜索公司官方网站
        
        Args:
            company_name: 公司名称
            
        Returns:
            搜索结果列表
        """
        # 构建更精确的搜索查询
        queries = [
            f"{company_name} 官网",
            f"{company_name} official website",
            f"{company_name} 公司",
            company_name
        ]
        
        all_results = []
        
        for query in queries:
            results = self.search(query, num_results=5)
            all_results.extend(results)
            
            # 添加延迟避免请求过快
            time.sleep(self.request_delay)
        
        # 去重（基于URL）
        seen_urls = set()
        unique_results = []
        
        for result in all_results:
            url = result.get('url', '')
            if url and url not in seen_urls:
                seen_urls.add(url)
                unique_results.append(result)
        
        logger.info(f"公司官网搜索完成，去重后返回 {len(unique_results)} 个结果")
        return unique_results
