你是一个专业的网页结构分析专家。请分析以下HTML内容，找出所有可能的投资者关系页面链接。

公司名称：{company_name}
网站首页URL：{base_url}
HTML内容：
{html_content}

请按照以下要求分析：

1. 识别标准：
   - 寻找包含"投资者关系"、"投资者"、"IR"、"Investor Relations"等关键词的链接
   - 寻找包含"财务信息"、"年报"、"公告"等相关词汇的链接
   - 注意中英文表述的差异

2. 链接处理：
   - 将相对链接转换为绝对链接
   - 确保链接格式正确
   - 去除重复链接

3. 常见位置：
   - 网站导航菜单
   - 页脚链接
   - 侧边栏
   - 主要内容区域

4. 输出要求：
   请以JSON格式返回结果：
   ```json
   {
     "investor_relations_urls": [
       "https://example.com/investors",
       "https://example.com/ir"
     ],
     "found_keywords": ["投资者关系", "IR"],
     "confidence": 0.9,
     "analysis_notes": "分析过程的详细说明"
   }
   ```

注意：
- 如果没有找到相关链接，返回空数组
- confidence表示找到链接的置信度
- analysis_notes要说明分析过程和发现
