"""
重试装饰器
"""
import time
import functools
from typing import Callable, Any, <PERSON><PERSON>, Type
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

def retry_on_exception(
    max_retries: int = 3,
    delay: float = 1.0,
    backoff: float = 2.0,
    exceptions: Tuple[Type[Exception], ...] = (Exception,),
    on_retry: Callable[[int, Exception], None] = None
):
    """
    重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff: 延迟倍数
        exceptions: 需要重试的异常类型
        on_retry: 重试时的回调函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        logger.error(f"函数 {func.__name__} 重试 {max_retries} 次后仍然失败: {e}")
                        raise e
                    
                    wait_time = delay * (backoff ** attempt)
                    logger.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {e}, {wait_time:.1f}秒后重试")
                    
                    if on_retry:
                        on_retry(attempt + 1, e)
                    
                    time.sleep(wait_time)
                except Exception as e:
                    # 对于不在重试列表中的异常，直接抛出
                    logger.error(f"函数 {func.__name__} 发生不可重试的异常: {e}")
                    raise e
            
            # 理论上不会到达这里
            if last_exception:
                raise last_exception
                
        return wrapper
    return decorator

def retry_with_exponential_backoff(
    max_retries: int = 3,
    initial_delay: float = 1.0,
    max_delay: float = 60.0,
    exceptions: Tuple[Type[Exception], ...] = (Exception,)
):
    """
    指数退避重试装饰器
    
    Args:
        max_retries: 最大重试次数
        initial_delay: 初始延迟时间
        max_delay: 最大延迟时间
        exceptions: 需要重试的异常类型
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        logger.error(f"函数 {func.__name__} 指数退避重试 {max_retries} 次后仍然失败: {e}")
                        raise e
                    
                    # 计算指数退避延迟时间
                    wait_time = min(initial_delay * (2 ** attempt), max_delay)
                    logger.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {e}, {wait_time:.1f}秒后重试")
                    
                    time.sleep(wait_time)
                except Exception as e:
                    logger.error(f"函数 {func.__name__} 发生不可重试的异常: {e}")
                    raise e
            
            if last_exception:
                raise last_exception
                
        return wrapper
    return decorator

class RetryManager:
    """重试管理器"""
    
    def __init__(self, max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
        self.max_retries = max_retries
        self.delay = delay
        self.backoff = backoff
    
    def execute_with_retry(
        self, 
        func: Callable, 
        *args, 
        exceptions: Tuple[Type[Exception], ...] = (Exception,),
        **kwargs
    ) -> Any:
        """
        执行函数并在失败时重试
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            exceptions: 需要重试的异常类型
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果
        """
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                return func(*args, **kwargs)
            except exceptions as e:
                last_exception = e
                
                if attempt == max_retries:
                    logger.error(f"函数执行重试 {self.max_retries} 次后仍然失败: {e}")
                    raise e
                
                wait_time = self.delay * (self.backoff ** attempt)
                logger.warning(f"函数执行第 {attempt + 1} 次尝试失败: {e}, {wait_time:.1f}秒后重试")
                
                time.sleep(wait_time)
            except Exception as e:
                logger.error(f"函数执行发生不可重试的异常: {e}")
                raise e
        
        if last_exception:
            raise last_exception
