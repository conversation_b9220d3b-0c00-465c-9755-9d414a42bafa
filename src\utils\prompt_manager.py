"""
Prompt管理器
支持从文件加载提示词并进行变量替换
"""
import os
import re
from typing import Dict, Any
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

class PromptManager:
    """AI提示词管理器"""
    
    def __init__(self, base_path: str = "docs"):
        """
        初始化Prompt管理器
        
        Args:
            base_path: 提示词文件的基础路径
        """
        self.base_path = base_path
        self._cache = {}  # 缓存已加载的提示词
    
    def load_prompt(self, file_path: str, variables: Dict[str, Any] = None) -> str:
        """
        加载提示词文件并进行变量替换
        
        Args:
            file_path: 相对于base_path的文件路径
            variables: 要替换的变量字典
            
        Returns:
            处理后的提示词文本
            
        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 变量替换失败
        """
        full_path = os.path.join(self.base_path, file_path)
        
        # 检查文件是否存在
        if not os.path.exists(full_path):
            raise FileNotFoundError(f"提示词文件不存在: {full_path}")
        
        # 从缓存或文件加载内容
        if full_path not in self._cache:
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                self._cache[full_path] = content
                logger.debug(f"已加载提示词文件: {full_path}")
            except Exception as e:
                logger.error(f"读取提示词文件失败: {full_path}, 错误: {e}")
                raise
        else:
            content = self._cache[full_path]
        
        # 进行变量替换
        if variables:
            try:
                content = self._replace_variables(content, variables)
                logger.debug(f"已完成变量替换，变量数量: {len(variables)}")
            except Exception as e:
                logger.error(f"变量替换失败: {e}")
                raise ValueError(f"变量替换失败: {e}")
        
        return content
    
    def _replace_variables(self, content: str, variables: Dict[str, Any]) -> str:
        """
        替换内容中的变量
        
        Args:
            content: 原始内容
            variables: 变量字典
            
        Returns:
            替换后的内容
        """
        # 查找所有变量占位符
        pattern = r'\{([^}]+)\}'
        placeholders = re.findall(pattern, content)
        
        # 检查是否有未提供的变量
        missing_vars = [var for var in placeholders if var not in variables]
        if missing_vars:
            raise ValueError(f"缺少必要的变量: {', '.join(missing_vars)}")
        
        # 执行替换
        for var_name, var_value in variables.items():
            placeholder = '{' + var_name + '}'
            content = content.replace(placeholder, str(var_value))
        
        return content
    
    def list_prompts(self, directory: str = "") -> list:
        """
        列出指定目录下的所有提示词文件
        
        Args:
            directory: 子目录路径
            
        Returns:
            文件路径列表
        """
        search_path = os.path.join(self.base_path, directory)
        prompt_files = []
        
        if os.path.exists(search_path):
            for root, dirs, files in os.walk(search_path):
                for file in files:
                    if file.endswith('.txt'):
                        rel_path = os.path.relpath(
                            os.path.join(root, file), 
                            self.base_path
                        )
                        prompt_files.append(rel_path.replace('\\', '/'))
        
        return sorted(prompt_files)
    
    def clear_cache(self):
        """清空缓存"""
        self._cache.clear()
        logger.debug("已清空提示词缓存")
