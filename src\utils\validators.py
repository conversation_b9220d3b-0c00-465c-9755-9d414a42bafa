"""
数据验证工具
"""
import re
from typing import Dict, Any, List, Optional
from urllib.parse import urlparse
from src.utils.exceptions import ValidationError
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def validate_url(url: str) -> bool:
        """
        验证URL格式是否正确
        
        Args:
            url: 要验证的URL
            
        Returns:
            URL是否有效
        """
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False
    
    @staticmethod
    def validate_company_name(company_name: str) -> bool:
        """
        验证公司名称是否有效
        
        Args:
            company_name: 公司名称
            
        Returns:
            公司名称是否有效
        """
        if not company_name or not isinstance(company_name, str):
            return False
        
        # 去除空白字符后检查长度
        cleaned_name = company_name.strip()
        if len(cleaned_name) < 2 or len(cleaned_name) > 100:
            return False
        
        # 检查是否包含有效字符（中文、英文、数字、常见符号）
        pattern = r'^[\u4e00-\u9fff\w\s\-\(\)\[\]\.&,]+$'
        return bool(re.match(pattern, cleaned_name))
    
    @staticmethod
    def validate_xpath(xpath: str) -> bool:
        """
        验证XPath表达式是否基本有效
        
        Args:
            xpath: XPath表达式
            
        Returns:
            XPath是否基本有效
        """
        if not xpath or not isinstance(xpath, str):
            return False
        
        # 基本的XPath格式检查
        xpath = xpath.strip()
        if not xpath.startswith('//') and not xpath.startswith('/'):
            return False
        
        # 检查是否包含基本的XPath元素
        xpath_patterns = [
            r'//\w+',  # 元素选择器
            r'\[@\w+',  # 属性选择器
            r'\[.*\]',  # 条件选择器
        ]
        
        return any(re.search(pattern, xpath) for pattern in xpath_patterns)
    
    @staticmethod
    def validate_research_result(result: Dict[str, Any]) -> bool:
        """
        验证调研结果数据结构
        
        Args:
            result: 调研结果字典
            
        Returns:
            数据结构是否有效
        """
        required_fields = ['company_name', 'base_url', 'investor_relations_urls', 'news_xpath_rules', 'status']
        
        # 检查必需字段
        for field in required_fields:
            if field not in result:
                logger.error(f"调研结果缺少必需字段: {field}")
                return False
        
        # 验证公司名称
        if not DataValidator.validate_company_name(result['company_name']):
            logger.error("调研结果中的公司名称无效")
            return False
        
        # 验证基础URL
        if result['base_url'] and not DataValidator.validate_url(result['base_url']):
            logger.error("调研结果中的基础URL无效")
            return False
        
        # 验证投资者关系URL列表
        if not isinstance(result['investor_relations_urls'], list):
            logger.error("投资者关系URL必须是列表")
            return False
        
        for url in result['investor_relations_urls']:
            if not DataValidator.validate_url(url):
                logger.error(f"投资者关系URL无效: {url}")
                return False
        
        # 验证XPath规则列表
        if not isinstance(result['news_xpath_rules'], list):
            logger.error("新闻XPath规则必须是列表")
            return False
        
        for xpath in result['news_xpath_rules']:
            if not DataValidator.validate_xpath(xpath):
                logger.error(f"XPath规则无效: {xpath}")
                return False
        
        # 验证状态
        valid_statuses = ['processing', 'completed', 'failed']
        if result['status'] not in valid_statuses:
            logger.error(f"调研状态无效: {result['status']}")
            return False
        
        return True
    
    @staticmethod
    def sanitize_company_name(company_name: str) -> str:
        """
        清理公司名称
        
        Args:
            company_name: 原始公司名称
            
        Returns:
            清理后的公司名称
        """
        if not company_name:
            return ""
        
        # 去除首尾空白
        cleaned = company_name.strip()
        
        # 去除多余的空白字符
        cleaned = re.sub(r'\s+', ' ', cleaned)
        
        # 去除特殊字符（保留基本的标点符号）
        cleaned = re.sub(r'[^\u4e00-\u9fff\w\s\-\(\)\[\]\.&,]', '', cleaned)
        
        return cleaned
    
    @staticmethod
    def normalize_url(url: str, base_url: str = None) -> str:
        """
        标准化URL
        
        Args:
            url: 原始URL
            base_url: 基础URL（用于处理相对链接）
            
        Returns:
            标准化后的URL
        """
        if not url:
            return ""
        
        url = url.strip()
        
        # 如果是相对链接且提供了基础URL
        if base_url and not url.startswith('http'):
            from urllib.parse import urljoin
            url = urljoin(base_url, url)
        
        # 移除URL片段标识符
        if '#' in url:
            url = url.split('#')[0]
        
        # 确保URL以http或https开头
        if not url.startswith(('http://', 'https://')):
            if url.startswith('//'):
                url = 'https:' + url
            elif not url.startswith('/'):
                url = 'https://' + url
        
        return url

def validate_input_data(data: Dict[str, Any], schema: Dict[str, Any]) -> None:
    """
    根据模式验证输入数据
    
    Args:
        data: 要验证的数据
        schema: 验证模式
        
    Raises:
        ValidationError: 数据验证失败
    """
    for field, rules in schema.items():
        if rules.get('required', False) and field not in data:
            raise ValidationError(f"缺少必需字段: {field}")
        
        if field in data:
            value = data[field]
            field_type = rules.get('type')
            
            if field_type and not isinstance(value, field_type):
                raise ValidationError(f"字段 {field} 类型错误，期望 {field_type.__name__}，实际 {type(value).__name__}")
            
            if 'validator' in rules:
                validator_func = rules['validator']
                if not validator_func(value):
                    raise ValidationError(f"字段 {field} 验证失败")

# 预定义的验证模式
COMPANY_RESEARCH_SCHEMA = {
    'company_name': {
        'required': True,
        'type': str,
        'validator': DataValidator.validate_company_name
    }
}

RESEARCH_RESULT_SCHEMA = {
    'company_name': {
        'required': True,
        'type': str,
        'validator': DataValidator.validate_company_name
    },
    'base_url': {
        'required': True,
        'type': str,
        'validator': lambda x: not x or DataValidator.validate_url(x)
    },
    'investor_relations_urls': {
        'required': True,
        'type': list
    },
    'news_xpath_rules': {
        'required': True,
        'type': list
    },
    'status': {
        'required': True,
        'type': str,
        'validator': lambda x: x in ['processing', 'completed', 'failed']
    }
}
