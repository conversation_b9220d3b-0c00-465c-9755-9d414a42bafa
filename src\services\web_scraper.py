"""
网页抓取服务
"""
import requests
import time
from typing import Optional
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from src.config import Config
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

class WebScraperService:
    """网页抓取服务类"""
    
    def __init__(self):
        """初始化网页抓取服务"""
        self.timeout = Config.SCRAPING_TIMEOUT
        self.max_retries = Config.MAX_RETRIES
        self.request_delay = Config.REQUEST_DELAY
        
        # 设置请求头，模拟真实浏览器
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        
        logger.info("网页抓取服务初始化完成")
    
    def fetch_page(self, url: str, encoding: str = 'utf-8') -> Optional[str]:
        """
        抓取网页内容
        
        Args:
            url: 目标URL
            encoding: 页面编码
            
        Returns:
            网页HTML内容，失败返回None
        """
        logger.info(f"开始抓取页面: {url}")
        
        for attempt in range(self.max_retries):
            try:
                # 发送请求
                response = requests.get(
                    url,
                    headers=self.headers,
                    timeout=self.timeout,
                    allow_redirects=True
                )
                
                # 检查响应状态
                if response.status_code == 200:
                    # 尝试自动检测编码
                    if response.encoding:
                        content = response.text
                    else:
                        # 如果无法检测编码，使用指定编码
                        response.encoding = encoding
                        content = response.text
                    
                    logger.info(f"页面抓取成功: {url}, 内容长度: {len(content)}")
                    return content
                    
                elif response.status_code in [301, 302, 303, 307, 308]:
                    # 处理重定向
                    redirect_url = response.headers.get('Location')
                    if redirect_url:
                        logger.info(f"页面重定向: {url} -> {redirect_url}")
                        return self.fetch_page(redirect_url, encoding)
                    
                else:
                    logger.warning(f"页面抓取失败，状态码: {response.status_code}, URL: {url}")
                    if attempt < self.max_retries - 1:
                        time.sleep(self.request_delay * (attempt + 1))
                        continue
                    else:
                        return None
                        
            except requests.exceptions.Timeout:
                logger.warning(f"页面抓取超时，尝试 {attempt + 1}/{self.max_retries}, URL: {url}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1))
                    continue
                else:
                    logger.error(f"页面抓取最终超时: {url}")
                    return None
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"页面抓取请求异常: {e}, URL: {url}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1))
                    continue
                else:
                    return None
                    
            except Exception as e:
                logger.error(f"页面抓取过程中发生未知错误: {e}, URL: {url}")
                return None
        
        return None
    
    def fetch_page_with_soup(self, url: str) -> Optional[BeautifulSoup]:
        """
        抓取网页并返回BeautifulSoup对象
        
        Args:
            url: 目标URL
            
        Returns:
            BeautifulSoup对象，失败返回None
        """
        html_content = self.fetch_page(url)
        if html_content:
            try:
                soup = BeautifulSoup(html_content, 'html.parser')
                return soup
            except Exception as e:
                logger.error(f"HTML解析失败: {e}, URL: {url}")
                return None
        return None
    
    def extract_links(self, html_content: str, base_url: str) -> list:
        """
        从HTML内容中提取所有链接
        
        Args:
            html_content: HTML内容
            base_url: 基础URL，用于处理相对链接
            
        Returns:
            链接列表
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            links = []
            
            for a_tag in soup.find_all('a', href=True):
                href = a_tag['href']
                text = a_tag.get_text(strip=True)
                
                # 处理相对链接
                if href.startswith('http'):
                    absolute_url = href
                else:
                    absolute_url = urljoin(base_url, href)
                
                links.append({
                    'url': absolute_url,
                    'text': text,
                    'href': href
                })
            
            logger.info(f"从页面提取到 {len(links)} 个链接")
            return links
            
        except Exception as e:
            logger.error(f"链接提取失败: {e}")
            return []
    
    def clean_html_content(self, html_content: str) -> str:
        """
        清理HTML内容，移除脚本、样式等不必要的内容
        
        Args:
            html_content: 原始HTML内容
            
        Returns:
            清理后的HTML内容
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 移除脚本和样式标签
            for tag in soup(['script', 'style', 'meta', 'link']):
                tag.decompose()
            
            # 移除注释
            from bs4 import Comment
            for comment in soup.find_all(string=lambda text: isinstance(text, Comment)):
                comment.extract()
            
            # 返回清理后的HTML
            cleaned_html = str(soup)
            logger.debug(f"HTML内容清理完成，原长度: {len(html_content)}, 清理后长度: {len(cleaned_html)}")
            
            return cleaned_html
            
        except Exception as e:
            logger.error(f"HTML清理失败: {e}")
            return html_content
    
    def is_valid_url(self, url: str) -> bool:
        """
        检查URL是否有效
        
        Args:
            url: 要检查的URL
            
        Returns:
            URL是否有效
        """
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False
