你是一个专业的网页结构分析和XPath专家。请分析以下投资者关系页面的HTML内容，提取新闻链接列表的XPath选择器规则。

公司名称：{company_name}
页面URL：{page_url}
HTML内容：
{html_content}

请按照以下要求分析：

1. 识别目标：
   - 寻找新闻列表、公告列表、新闻发布等区域
   - 识别包含新闻标题和链接的HTML元素
   - 关注日期信息的存在

2. 分析步骤：
   - 查找包含新闻列表的容器元素
   - 分析列表项的HTML结构
   - 识别链接元素的选择器模式
   - 验证XPath的准确性

3. XPath规则要求：
   - 提供多个可能的XPath规则（按优先级排序）
   - 确保XPath能准确选择到新闻链接
   - 考虑页面结构的变化可能性

4. 常见模式：
   - 新闻列表通常在ul/li、div、table结构中
   - 链接通常是a标签，包含href属性
   - 可能包含标题、日期、摘要等信息

5. 输出要求：
   请以JSON格式返回结果：
   ```json
   {
     "xpath_rules": [
       "//div[@class='news-list']//a[@href]",
       "//ul[@id='press-releases']//li//a",
       "//table[@class='news-table']//td//a[@href]"
     ],
     "primary_xpath": "//div[@class='news-list']//a[@href]",
     "confidence": 0.85,
     "structure_analysis": "页面结构的详细分析",
     "sample_links": [
       "https://example.com/news/1",
       "https://example.com/news/2"
     ]
   }
   ```

注意：
- xpath_rules按优先级排序，第一个是最推荐的
- primary_xpath是主要推荐的XPath规则
- confidence表示XPath规则的可靠性
- sample_links提供找到的示例链接（最多5个）
