"""
配置管理模块
从环境变量中加载配置信息
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """应用配置类"""
    
    # AI API配置
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
    OPENAI_BASE_URL = os.getenv('OPENAI_BASE_URL')
    OPENAI_MODEL = os.getenv('OPENAI_MODEL', 'gpt-4o-mini')
    OPENAI_TIMEOUT = int(os.getenv('OPENAI_TIMEOUT', 60))
    
    # Google搜索API配置
    GOOGLE_SEARCH_API_URL = os.getenv('GOOGLE_SEARCH_API_URL')
    
    # 数据库配置
    MYSQL_HOST = os.getenv('MYSQL_HOST')
    MYSQL_PORT = int(os.getenv('MYSQL_PORT', 3306))
    MYSQL_USER = os.getenv('MYSQL_USER')
    MYSQL_PASSWORD = os.getenv('MYSQL_PASSWORD')
    MYSQL_DATABASE = os.getenv('MYSQL_DATABASE')
    
    # 网页抓取配置
    SCRAPING_TIMEOUT = int(os.getenv('SCRAPING_TIMEOUT', 30))
    MAX_RETRIES = int(os.getenv('MAX_RETRIES', 3))
    REQUEST_DELAY = int(os.getenv('REQUEST_DELAY', 1))
    
    # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'logs/app.log')
    
    @classmethod
    def validate(cls):
        """验证必要的配置项是否存在"""
        required_configs = [
            'OPENAI_API_KEY',
            'GOOGLE_SEARCH_API_URL'
        ]
        
        missing_configs = []
        for config in required_configs:
            if not getattr(cls, config):
                missing_configs.append(config)
        
        if missing_configs:
            raise ValueError(f"缺少必要的配置项: {', '.join(missing_configs)}")
        
        return True
