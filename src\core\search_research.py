"""
核心搜索调研类
实现公司调研的完整流程
"""
import json
import time
from typing import Dict, List, Any, Optional
from urllib.parse import urljoin, urlparse

from src.config import Config
from src.utils.logger import setup_logger
from src.utils.prompt_manager import PromptManager
from src.services.google_search import GoogleSearchService
from src.services.web_scraper import WebScraperService
from src.services.ai_analyzer import AIAnalyzerService

logger = setup_logger(__name__)

class SearchResearchClass:
    """公司搜索调研类"""
    
    def __init__(self):
        """初始化搜索调研类"""
        self.prompt_manager = PromptManager()
        self.google_search = GoogleSearchService()
        self.web_scraper = WebScraperService()
        self.ai_analyzer = AIAnalyzerService()
        
        logger.info("SearchResearchClass 初始化完成")
    
    def research_company(self, company_name: str) -> Dict[str, Any]:
        """
        执行完整的公司调研流程
        
        Args:
            company_name: 公司名称
            
        Returns:
            结构化的调研结果
        """
        logger.info(f"开始调研公司: {company_name}")
        
        try:
            # 初始化结果结构
            result = {
                "company_name": company_name,
                "base_url": "",
                "investor_relations_urls": [],
                "news_xpath_rules": [],
                "research_timestamp": time.time(),
                "status": "processing"
            }
            
            # 步骤1: 搜索公司官网
            logger.info("步骤1: 搜索公司官网")
            base_url = self._search_official_website(company_name)
            if not base_url:
                result["status"] = "failed"
                result["error"] = "无法找到公司官网"
                return result
            
            result["base_url"] = base_url
            logger.info(f"找到官网: {base_url}")
            
            # 步骤2: 分析官网首页，提取投资者关系页面
            logger.info("步骤2: 分析官网首页")
            investor_urls = self._extract_investor_relations_urls(company_name, base_url)
            result["investor_relations_urls"] = investor_urls
            logger.info(f"找到投资者关系页面: {len(investor_urls)}个")
            
            # 步骤3: 分析投资者关系页面，提取新闻XPath规则
            logger.info("步骤3: 分析投资者关系页面")
            xpath_rules = self._extract_news_xpath_rules(company_name, investor_urls)
            result["news_xpath_rules"] = xpath_rules
            logger.info(f"提取到XPath规则: {len(xpath_rules)}个")
            
            result["status"] = "completed"
            logger.info(f"公司调研完成: {company_name}")
            
            return result
            
        except Exception as e:
            logger.error(f"公司调研失败: {company_name}, 错误: {e}")
            return {
                "company_name": company_name,
                "status": "failed",
                "error": str(e),
                "research_timestamp": time.time()
            }
    
    def _search_official_website(self, company_name: str) -> Optional[str]:
        """
        搜索公司官方网站
        
        Args:
            company_name: 公司名称
            
        Returns:
            官网URL，如果未找到返回None
        """
        try:
            # 执行Google搜索
            search_results = self.google_search.search(company_name)
            if not search_results:
                logger.warning(f"Google搜索无结果: {company_name}")
                return None
            
            # 使用AI分析搜索结果
            prompt = self.prompt_manager.load_prompt(
                'search/find_official_website.txt',
                {
                    'company_name': company_name,
                    'search_results': json.dumps(search_results, ensure_ascii=False, indent=2)
                }
            )
            
            analysis_result = self.ai_analyzer.analyze(prompt)
            
            # 解析AI分析结果
            try:
                result_data = json.loads(analysis_result)
                official_website = result_data.get('official_website')
                confidence = result_data.get('confidence', 0)
                
                if official_website and confidence > 0.5:
                    return official_website
                else:
                    logger.warning(f"AI分析置信度过低: {confidence}")
                    return None
                    
            except json.JSONDecodeError:
                logger.error(f"AI分析结果解析失败: {analysis_result}")
                return None
                
        except Exception as e:
            logger.error(f"搜索官网失败: {e}")
            return None

    def _extract_investor_relations_urls(self, company_name: str, base_url: str) -> List[str]:
        """
        从官网首页提取投资者关系页面URL

        Args:
            company_name: 公司名称
            base_url: 官网首页URL

        Returns:
            投资者关系页面URL列表
        """
        try:
            # 抓取首页HTML内容
            html_content = self.web_scraper.fetch_page(base_url)
            if not html_content:
                logger.warning(f"无法获取首页内容: {base_url}")
                return []

            # 使用AI分析HTML内容
            prompt = self.prompt_manager.load_prompt(
                'analysis/extract_investor_relations.txt',
                {
                    'company_name': company_name,
                    'base_url': base_url,
                    'html_content': html_content[:50000]  # 限制内容长度
                }
            )

            analysis_result = self.ai_analyzer.analyze(prompt)

            # 解析AI分析结果
            try:
                result_data = json.loads(analysis_result)
                urls = result_data.get('investor_relations_urls', [])
                confidence = result_data.get('confidence', 0)

                if confidence > 0.3:  # 较低的置信度阈值
                    # 处理相对链接
                    processed_urls = []
                    for url in urls:
                        if url.startswith('http'):
                            processed_urls.append(url)
                        else:
                            absolute_url = urljoin(base_url, url)
                            processed_urls.append(absolute_url)

                    return processed_urls
                else:
                    logger.warning(f"投资者关系页面分析置信度过低: {confidence}")
                    return []

            except json.JSONDecodeError:
                logger.error(f"投资者关系页面分析结果解析失败: {analysis_result}")
                return []

        except Exception as e:
            logger.error(f"提取投资者关系页面失败: {e}")
            return []

    def _extract_news_xpath_rules(self, company_name: str, investor_urls: List[str]) -> List[str]:
        """
        从投资者关系页面提取新闻链接的XPath规则

        Args:
            company_name: 公司名称
            investor_urls: 投资者关系页面URL列表

        Returns:
            XPath规则列表
        """
        all_xpath_rules = []

        for url in investor_urls:
            try:
                logger.info(f"分析投资者关系页面: {url}")

                # 抓取页面HTML内容
                html_content = self.web_scraper.fetch_page(url)
                if not html_content:
                    logger.warning(f"无法获取页面内容: {url}")
                    continue

                # 使用AI分析HTML内容
                prompt = self.prompt_manager.load_prompt(
                    'extraction/extract_news_xpath.txt',
                    {
                        'company_name': company_name,
                        'page_url': url,
                        'html_content': html_content[:50000]  # 限制内容长度
                    }
                )

                analysis_result = self.ai_analyzer.analyze(prompt)

                # 解析AI分析结果
                try:
                    result_data = json.loads(analysis_result)
                    xpath_rules = result_data.get('xpath_rules', [])
                    confidence = result_data.get('confidence', 0)

                    if confidence > 0.5 and xpath_rules:
                        all_xpath_rules.extend(xpath_rules)
                        logger.info(f"从 {url} 提取到 {len(xpath_rules)} 个XPath规则")
                    else:
                        logger.warning(f"XPath规则提取置信度过低: {confidence}")

                except json.JSONDecodeError:
                    logger.error(f"XPath规则分析结果解析失败: {analysis_result}")
                    continue

            except Exception as e:
                logger.error(f"分析投资者关系页面失败: {url}, 错误: {e}")
                continue

            # 添加延迟避免请求过快
            time.sleep(Config.REQUEST_DELAY)

        # 去重并返回
        unique_rules = list(set(all_xpath_rules))
        logger.info(f"总共提取到 {len(unique_rules)} 个唯一XPath规则")

        return unique_rules
